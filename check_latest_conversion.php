<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\BiTokenConversionRecord;

echo "=== 最新的5条BiToken转换记录 ===\n";
$records = BiTokenConversionRecord::with('user')->latest()->take(5)->get();

foreach ($records as $record) {
    echo "---\n";
    echo "ID: {$record->id}\n";
    echo "用户: {$record->user->username}\n";
    echo "BiToken数量: {$record->bitoken_amount}\n";
    echo "锁仓WT数量: {$record->locked_wt_amount}\n";
    echo "状态: {$record->status_text}\n";
    echo "用户中心扣除: " . ($record->center_deducted ? '已扣除' : '未扣除') . "\n";
    echo "错误信息: " . ($record->error_message ?: '无') . "\n";
    echo "创建时间: {$record->created_at}\n";
}
