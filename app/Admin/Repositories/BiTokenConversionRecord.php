<?php

namespace App\Admin\Repositories;

use App\Models\BiTokenConversionRecord as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class BiTokenConversionRecord extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 设置查询关联
     */
    public function with()
    {
        return ['user', 'lockedWt'];
    }
}
