<?php

namespace App\Admin\Controllers\Shinami;

use App\Admin\Repositories\BiTokenConversionRecord;
use App\Models\BiTokenConversionRecord as BiTokenConversionRecordModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;

class BiTokenConversionController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new BiTokenConversionRecord(), function (Grid $grid) {
            $grid->column('id')->sortable();
            
            $grid->column('user.username', '用户名')->display(function ($username) {
                return $username ?: '未知用户';
            });
            
            $grid->column('center_key', '用户中心KEY')->limit(20);
            
            $grid->column('bitoken_amount', 'BiToken数量')->display(function ($amount) {
                return number_format($amount, 8);
            })->sortable();
            
            $grid->column('locked_wt_amount', '锁仓WT数量')->display(function ($amount) {
                return number_format($amount, 8);
            })->sortable();
            
            $grid->column('conversion_rate', '转换比例')->display(function ($rate) {
                return "1:{$rate}";
            });
            
            $grid->column('status', '状态')->using(BiTokenConversionRecordModel::STATUS_MAP)->label([
                BiTokenConversionRecordModel::STATUS_PENDING => 'warning',
                BiTokenConversionRecordModel::STATUS_COMPLETED => 'success',
                BiTokenConversionRecordModel::STATUS_FAILED => 'danger',
            ]);
            
            $grid->column('locked_wt_id', '锁仓记录ID')->display(function ($id) {
                if ($id) {
                    return "<a href='" . admin_url("shinami/locked-wt/{$id}") . "' target='_blank'>#{$id}</a>";
                }
                return '-';
            });
            
            $grid->column('error_message', '错误信息')->limit(50)->help('转换失败时的错误信息');
            
            $grid->column('created_at', '转换时间')->sortable();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '用户ID');
                $filter->like('user.username', '用户名');
                $filter->like('center_key', '用户中心KEY');
                $filter->equal('status', '状态')->select(BiTokenConversionRecordModel::STATUS_MAP);
                $filter->between('bitoken_amount', 'BiToken数量');
                $filter->between('locked_wt_amount', '锁仓WT数量');
                $filter->between('created_at', '转换时间')->datetime();
            });
            
            // 默认排序
            $grid->model()->orderBy('created_at', 'desc');
            
            // 禁用操作
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            
            // 工具栏
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="' . admin_url('shinami/bitoken-conversion/stats') . '" class="btn btn-primary btn-sm">
                    <i class="fa fa-bar-chart"></i> 转换统计
                </a>');
            });
            
            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->disableDelete();
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new BiTokenConversionRecord(), function (Show $show) {
            $show->field('id', 'ID');
            
            $show->field('user.username', '用户名');
            $show->field('user.email', '用户邮箱');
            $show->field('center_key', '用户中心KEY');
            
            $show->field('bitoken_amount', 'BiToken数量')->as(function ($amount) {
                return number_format($amount, 8);
            });
            
            $show->field('locked_wt_amount', '锁仓WT数量')->as(function ($amount) {
                return number_format($amount, 8);
            });
            
            $show->field('conversion_rate', '转换比例')->as(function ($rate) {
                return "1:{$rate}";
            });
            
            $show->field('status', '状态')->using(BiTokenConversionRecordModel::STATUS_MAP);
            
            $show->field('locked_wt_id', '锁仓记录ID')->link(function ($id) {
                if ($id) {
                    return admin_url("shinami/locked-wt/{$id}");
                }
                return null;
            });
            
            $show->field('error_message', '错误信息');
            
            $show->field('created_at', '转换时间');
            $show->field('updated_at', '更新时间');
            
            // 关联锁仓记录信息
            if ($this->locked_wt_id) {
                $show->divider();
                $show->field('lockedWt.amount', '锁仓总数量')->as(function ($amount) {
                    return number_format($amount, 8) . ' WT';
                });
                $show->field('lockedWt.remaining_amount', '锁仓剩余数量')->as(function ($amount) {
                    return number_format($amount, 8) . ' WT';
                });
                $show->field('lockedWt.status_text', '锁仓状态');
                $show->field('lockedWt.expired_at', '锁仓到期时间');
            }
        });
    }

    /**
     * 转换统计页面
     */
    public function stats()
    {
        $stats = [
            'total_records' => BiTokenConversionRecordModel::count(),
            'completed_records' => BiTokenConversionRecordModel::where('status', BiTokenConversionRecordModel::STATUS_COMPLETED)->count(),
            'failed_records' => BiTokenConversionRecordModel::where('status', BiTokenConversionRecordModel::STATUS_FAILED)->count(),
            'pending_records' => BiTokenConversionRecordModel::where('status', BiTokenConversionRecordModel::STATUS_PENDING)->count(),
            'total_bitoken' => BiTokenConversionRecordModel::where('status', BiTokenConversionRecordModel::STATUS_COMPLETED)->sum('bitoken_amount'),
            'total_locked_wt' => BiTokenConversionRecordModel::where('status', BiTokenConversionRecordModel::STATUS_COMPLETED)->sum('locked_wt_amount'),
        ];
        
        return view('admin.bitoken-conversion-stats', compact('stats'));
    }
}
