<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BiTokenConversionRecord extends Model
{
    use BelongsToUser;

    protected $table = 'user_bitoken_conversion_records';

    protected $casts = [
        'bitoken_amount'   => 'decimal:8',
        'locked_wt_amount' => 'decimal:8',
        'conversion_rate'  => 'decimal:2',
    ];

    // 转换状态常量
    const STATUS_PENDING   = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED    = 'failed';

    const STATUS_MAP = [
        self::STATUS_PENDING   => '处理中',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_FAILED    => '失败',
    ];

    /**
     * 关联锁仓WT记录
     */
    public function lockedWt(): BelongsTo
    {
        return $this->belongsTo(LockedWt::class, 'locked_wt_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return self::STATUS_MAP[$this->status] ?? '未知状态';
    }

    /**
     * 标记为完成
     */
    public function markAsCompleted(int $lockedWtId): void
    {
        $this->update([
            'status'       => self::STATUS_COMPLETED,
            'locked_wt_id' => $lockedWtId,
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status'        => self::STATUS_FAILED,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * 获取用户的转换记录统计
     */
    public static function getUserConversionStats(int $userId): array
    {
        $records = self::where('user_id', $userId)->get();

        return [
            'total_count'     => $records->count(),
            'completed_count' => $records->where('status', self::STATUS_COMPLETED)->count(),
            'failed_count'    => $records->where('status', self::STATUS_FAILED)->count(),
            'total_bitoken'   => $records->where('status', self::STATUS_COMPLETED)->sum('bitoken_amount'),
            'total_locked_wt' => $records->where('status', self::STATUS_COMPLETED)->sum('locked_wt_amount'),
        ];
    }
}
