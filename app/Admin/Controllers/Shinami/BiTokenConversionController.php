<?php

namespace App\Admin\Controllers\Shinami;

use App\Models\BiTokenConversionRecord;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;

class BiTokenConversionController extends AdminController
{
    /**
     * 格式化数字，去除末尾的0
     */
    public static function formatNumber($number, $decimals = 8)
    {
        return rtrim(rtrim(number_format($number, $decimals, '.', ''), '0'), '.');
    }
    protected string $title = 'BiToken转换记录';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(BiTokenConversionRecord::with(['user', 'lockedWt']), function (Grid $grid) {
            $grid->column('id')->sortable();

            $grid->column('user.username', '用户名')->display(function ($username) {
                return $username ?: '未知用户';
            });

            $grid->column('center_key', '用户中心KEY')->limit(20);

            $grid->column('bitoken_amount', 'BiToken数量')
                ->display(function ($amount) {
                    return self::formatNumber($amount);
                })->sortable();

            $grid->column('locked_wt_amount', '锁仓WT数量')
                ->display(function ($amount) {
                    return self::formatNumber($amount);
                })->sortable();

            $grid->column('conversion_rate', '转换比例')
                ->display(function ($rate) {
                    return "1:".self::formatNumber($rate, 2);
                });

            $grid->column('status', '状态')
                ->using(BiTokenConversionRecord::STATUS_MAP)
                ->label([
                    BiTokenConversionRecord::STATUS_PENDING => 'warning',
                    BiTokenConversionRecord::STATUS_COMPLETED => 'success',
                    BiTokenConversionRecord::STATUS_FAILED => 'danger',
                ]);

            $grid->column('locked_wt_id', '锁仓记录ID')
                ->display(function ($id) {
                    if ($id) {
                        return "<a href='".admin_url("shinami/locked-wt/{$id}")."' target='_blank'>#{$id}</a>";
                    }
                    return '-';
                });

            $grid->column('center_deducted', '用户中心扣除')->display(function ($deducted) {
                return $deducted ? '<span class="badge badge-success">已扣除</span>' : '<span class="badge badge-warning">未扣除</span>';
            });

            $grid->column('error_message', '错误信息')->limit(50)->help('转换失败时的错误信息');

            $grid->column('created_at', '转换时间')->sortable();

            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '用户ID');
                $filter->like('user.username', '用户名');
                $filter->like('center_key', '用户中心KEY');
                $filter->equal('status', '状态')->select(BiTokenConversionRecord::STATUS_MAP);
                $filter->between('bitoken_amount', 'BiToken数量');
                $filter->between('locked_wt_amount', '锁仓WT数量');
                $filter->between('created_at', '转换时间')->datetime();
            });

            // 默认排序
            $grid->model()->orderBy('created_at', 'desc');

            // 禁用操作
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableActions();
            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->disableDelete();
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, BiTokenConversionRecord::with(['user', 'lockedWt']), function (Show $show) {
            $show->field('id', 'ID');

            $show->field('user.username', '用户名');
            $show->field('user.email', '用户邮箱');
            $show->field('center_key', '用户中心KEY');

            $show->field('bitoken_amount', 'BiToken数量')->as(function ($amount) {
                return self::formatNumber($amount);
            });

            $show->field('locked_wt_amount', '锁仓WT数量')->as(function ($amount) {
                return self::formatNumber($amount);
            });

            $show->field('conversion_rate', '转换比例')->as(function ($rate) {
                return "1:{$rate}";
            });

            $show->field('status', '状态')->using(BiTokenConversionRecord::STATUS_MAP);

            $show->field('locked_wt_id', '锁仓记录ID')->link(function ($id) {
                if ($id) {
                    return admin_url("shinami/locked-wt/{$id}");
                }
                return null;
            });

            $show->field('center_deducted', '用户中心扣除状态')->as(function ($deducted) {
                return $deducted ? '已扣除' : '未扣除';
            });

            $show->field('center_deduct_result', '用户中心扣除结果')->json();

            $show->field('error_message', '错误信息');

            $show->field('created_at', '转换时间');
            $show->field('updated_at', '更新时间');

            // 关联锁仓记录信息
            if ($this->locked_wt_id) {
                $show->divider();
                $show->field('lockedWt.amount', '锁仓总数量')->as(function ($amount) {
                    return self::formatNumber($amount).' WT';
                });
                $show->field('lockedWt.remaining_amount', '锁仓剩余数量')->as(function ($amount) {
                    return self::formatNumber($amount).' WT';
                });
                $show->field('lockedWt.status_text', '锁仓状态');
                $show->field('lockedWt.expired_at', '锁仓到期时间');
            }
        });
    }

}
