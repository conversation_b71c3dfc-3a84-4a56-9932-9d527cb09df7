<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\BiTokenConversionRecord;
use App\Models\LockedWt;
use App\Models\LockedWtOperation;

echo "=== BiToken转换记录 ===\n";
$records = BiTokenConversionRecord::with('user')->get();
foreach ($records as $record) {
    echo "用户: {$record->user->username}\n";
    echo "BiToken: {$record->bitoken_amount}\n";
    echo "锁仓WT: {$record->locked_wt_amount}\n";
    echo "状态: {$record->status_text}\n";
    echo "创建时间: {$record->created_at}\n";
    echo "---\n";
}

echo "\n=== 锁仓WT记录 ===\n";
$lockedWts = LockedWt::with('user')->where('user_id', 1)->get();
foreach ($lockedWts as $lockedWt) {
    echo "用户: {$lockedWt->user->username}\n";
    echo "数量: {$lockedWt->amount}\n";
    echo "剩余: {$lockedWt->remaining_amount}\n";
    echo "状态: {$lockedWt->status_text}\n";
    echo "到期时间: {$lockedWt->expired_at}\n";
    echo "---\n";
}

echo "\n=== 操作记录 ===\n";
$operations = LockedWtOperation::with('user')->where('operation_type', 'bitoken_conversion')->get();
foreach ($operations as $operation) {
    echo "用户: {$operation->user->username}\n";
    echo "操作类型: {$operation->operation_type}\n";
    echo "数量: {$operation->amount}\n";
    echo "备注: {$operation->remark}\n";
    echo "创建时间: {$operation->created_at}\n";
    echo "---\n";
}
