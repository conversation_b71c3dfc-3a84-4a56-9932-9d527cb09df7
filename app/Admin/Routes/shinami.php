<?php

use App\Admin\Controllers\Shinami\BiTokenConversionController;
use App\Admin\Controllers\Shinami\WalletController;
use App\Admin\Controllers\Shinami\WalletLogController;
use App\Admin\Controllers\Shinami\WalletTransactionController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
], function (Router $router) {
    $router->resource('shinami/wallets', WalletController::class);
    $router->resource('shinami/wallet_logs', WalletLogController::class);
    $router->resource('shinami/transactions', WalletTransactionController::class);
    $router->get('shinami/wallet/admin', [WalletController::class, 'adminWallet']);

    // BiToken转换记录
    $router->resource('shinami/bitoken-conversion', BiTokenConversionController::class);
    $router->get('shinami/bitoken-conversion-stats', [BiTokenConversionController::class, 'stats']);
});
