<?php

function formatNumber($number, $decimals = 8)
{
    return rtrim(rtrim(number_format($number, $decimals, '.', ''), '0'), '.');
}

// 测试转换比例格式
$testRates = [
    20.00,
    20.50,
    15.25,
    10.00,
    1.00,
    0.50,
];

echo "转换比例格式化测试:\n";
foreach ($testRates as $rate) {
    $original = "1:{$rate}";
    $formatted = "1:".formatNumber($rate, 2);
    echo "原始: {$original} -> 格式化: {$formatted}\n";
}
