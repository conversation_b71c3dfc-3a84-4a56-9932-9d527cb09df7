<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_locked_wt', function (Blueprint $table) {
            $table->decimal('amount', 25, 8)->change();
            $table->decimal('remaining_amount', 25, 8)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_locked_wt', function (Blueprint $table) {
            //
        });
    }
};
