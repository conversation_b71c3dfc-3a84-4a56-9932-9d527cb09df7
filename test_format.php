<?php

function formatNumber($number, $decimals = 8)
{
    return rtrim(rtrim(number_format($number, $decimals, '.', ''), '0'), '.');
}

// 测试不同的数字格式
$testNumbers = [
    5.50000000,
    110.00000000,
    1998000.00000000,
    0.12345678,
    1.00000000,
    0.10000000,
    123.45600000,
];

echo "数字格式化测试:\n";
foreach ($testNumbers as $number) {
    $original = number_format($number, 8);
    $formatted = formatNumber($number);
    echo "原始: {$original} -> 格式化: {$formatted}\n";
}
