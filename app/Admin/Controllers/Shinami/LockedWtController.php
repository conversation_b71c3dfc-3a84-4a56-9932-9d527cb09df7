<?php

namespace App\Admin\Controllers\Shinami;

use App\Admin\Renders\Shinami\LockedWtOperations;
use App\Models\LockedWt;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class LockedWtController extends AdminController
{
    protected string $title = '锁仓WT';

    /**
     * Make a grid builder.
     */
    protected function grid(): Grid
    {
        return Grid::make(LockedWt::with('user.info', 'transferFromUser.info'), function (Grid $grid) {
            $grid->disableEditButton();
            $grid->column('id', 'ID')->sortable();
            $grid->column('user', '用户')
                ->display(function () {
                    return $this->user?->show_name;
                });
            $grid->column('amount', '原始数量')
                ->display(function ($amount) {
                    return floatval($amount).' WT';
                });
            $grid->column('remaining_amount', '剩余数量')
                ->display(function ($amount) {
                    return floatval($amount).' WT';
                });
            $grid->column('transferred_amount', '已转出')
                ->display(function () {
                    $transferred = $this->getTransferredAmount();
                    return $transferred > 0 ? floatval($transferred).' WT' : '-';
                });
            $grid->column('expired_at', '到期时间')
                ->display(function ($expired_at) {
                    if (! $expired_at) {
                        return '-';
                    }
                    $isExpired = now()->gt($expired_at);
                    return $expired_at.($isExpired ? ' (已过期)' : '');
                });

            $grid->column('status', '状态')
                ->using(LockedWt::STATUS_MAP);

            $grid->column('transfer_remark', '备注')
                ->display(function ($remark) {
                    return $remark ? mb_substr($remark, 0, 20).(mb_strlen($remark) > 20 ? '...' : '') : '-';
                });

            $grid->column('created_at', '创建时间');

            // 添加操作记录列
            $grid->column('operations', '操作记录')
                ->display('详情')
                ->modal('详情', function () {
                    return LockedWtOperations::make()->payload(['key' => $this->id]);
                });
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户账号');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('status', '状态')->select(LockedWt::STATUS_MAP);
                $filter->between('expired_at', '到期时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();
            });
            // 默认排序
            $grid->model()->orderBy('created_at', 'desc');
        });
    }

    /**
     * Make a form builder.
     */
    protected function form(): Form
    {
        return Form::make(LockedWt::with('user'), function (Form $form) {
            $form->display('id', 'ID');

            $form->select('user_id', '隶属用户')
                ->options(function ($userId) {
                    if ($userId) {
                        return [$userId => User::find($userId)->showName];
                    } else {
                        return [];
                    }
                })
                ->ajax(route('admin.user.users.ajax'))
                ->required();

            $form->decimal('amount', '锁定数量')
                ->required()
                ->help('请输入要锁定的WT数量');

            $form->datetime('expired_at', '到期时间')
                ->required()
                ->help('锁定到期时间，过期后将自动解锁');
            $form->radio('status', '状态')
                ->options(LockedWt::STATUS_MAP)
                ->default(LockedWt::STATUS_LOCKED);
            $form->saving(function (Form $form) {
                $expired_at = $form->expired_at;
                if ($expired_at && $expired_at < now()) {
                    return $form->response()->error('到期时间不能早于当前时间');
                }
                //到期时间必须大于当前时间两个月
                if ($expired_at && $expired_at < now()->addMonths(2)) {
                    return $form->response()->error('到期时间必须大于当前时间两个月');
                }
            });
        });
    }

}
